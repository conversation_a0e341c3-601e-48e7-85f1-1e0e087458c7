const { app, BrowserWindow, Tray, Menu, ipcMain, globalShortcut, dialog, Notification } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

// Import services
const AudioService = require('./services/AudioService');
const TranscriptionService = require('./services/TranscriptionService');
const TypingService = require('./services/TypingService');
const SettingsService = require('./services/SettingsService');

class KambaVoiceApp {
  constructor() {
    this.mainWindow = null;
    this.settingsWindow = null;
    this.recordingWindow = null;
    this.tray = null;
    this.isRecording = false;

    // Initialize services
    this.audioService = new AudioService();
    this.transcriptionService = new TranscriptionService();
    this.typingService = new TypingService();
    this.settingsService = new SettingsService();

    this.initializeApp();
  }

  initializeApp() {
    // Handle app ready
    app.whenReady().then(() => {
      this.createMainWindow(); // Show main window immediately
      this.registerGlobalShortcuts();
      this.setupIpcHandlers();

      // On macOS, re-create window when dock icon is clicked
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Quit when all windows are closed (except on macOS)
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle app before quit
    app.on('before-quit', () => {
      this.cleanup();
    });
  }

  createMainWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../shared/preload.js')
      }
    });

    // Load the renderer - for now just load the HTML file directly
    this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    this.mainWindow.webContents.openDevTools();

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Just close normally for now
    this.mainWindow.on('close', () => {
      app.quit();
    });
  }

  createSettingsWindow() {
    if (this.settingsWindow) {
      this.settingsWindow.focus();
      return;
    }

    this.settingsWindow = new BrowserWindow({
      width: 600,
      height: 500,
      resizable: false,
      parent: this.mainWindow,
      modal: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../shared/preload.js')
      }
    });

    if (isDev) {
      this.settingsWindow.loadURL('http://localhost:3000/settings');
    } else {
      this.settingsWindow.loadFile(path.join(__dirname, '../renderer/settings.html'));
    }

    this.settingsWindow.on('closed', () => {
      this.settingsWindow = null;
    });
  }

  createRecordingWindow() {
    this.recordingWindow = new BrowserWindow({
      width: 300,
      height: 80,
      frame: false,
      alwaysOnTop: true,
      transparent: true,
      resizable: false,
      skipTaskbar: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../shared/preload.js')
      }
    });

    // Center the window at the top of the screen
    const { screen } = require('electron');
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width } = primaryDisplay.workAreaSize;
    this.recordingWindow.setPosition(Math.round((width - 300) / 2), 50);

    if (isDev) {
      this.recordingWindow.loadURL('http://localhost:3000/recording');
    } else {
      this.recordingWindow.loadFile(path.join(__dirname, '../renderer/recording.html'));
    }

    this.recordingWindow.on('closed', () => {
      this.recordingWindow = null;
    });
  }

  // Tray functionality removed for simplicity

  registerGlobalShortcuts() {
    // Default shortcut: Ctrl+Shift+V
    const shortcut = 'CommandOrControl+Shift+V';
    
    globalShortcut.register(shortcut, () => {
      this.handleVoiceShortcut();
    });

    console.log(`Global shortcut ${shortcut} registered`);
  }

  handleVoiceShortcut() {
    if (!this.isRecording) {
      this.startRecording();
    } else {
      this.stopRecording();
    }
  }

  async startRecording() {
    if (this.isRecording) {
      console.log('Already recording');
      return { success: false, message: 'Already recording' };
    }

    try {
      console.log('Starting voice recording...');

      // Set up transcription service with current API key if available
      const openAISettings = this.settingsService.getOpenAISettings();
      if (openAISettings.apiKey) {
        this.transcriptionService.setApiKey(openAISettings.apiKey);
      }

      // Start audio recording
      const audioPath = await this.audioService.startRecording();
      this.isRecording = true;

      // Create and show recording window
      this.createRecordingWindow();

      // Notify renderer processes
      if (this.mainWindow) {
        this.mainWindow.webContents.send('recording-started');
      }
      if (this.recordingWindow) {
        this.recordingWindow.webContents.send('recording-started');
      }

      return { success: true, audioPath };
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.isRecording = false;
      return { success: false, message: error.message };
    }
  }

  async stopRecording() {
    if (!this.isRecording) {
      console.log('Not currently recording');
      return { success: false, message: 'Not currently recording' };
    }

    try {
      console.log('Stopping voice recording...');

      // Stop audio recording and get file path
      const audioPath = await this.audioService.stopRecording();
      this.isRecording = false;

      // Hide recording window
      if (this.recordingWindow) {
        this.recordingWindow.close();
      }

      // Notify renderer processes
      if (this.mainWindow) {
        this.mainWindow.webContents.send('recording-stopped');
      }

      // Process the audio for transcription
      this.processRecordedAudio(audioPath).catch(error => {
        console.error('Audio processing failed:', error);
        // Show error notification
        if (this.settingsService.getSetting('showNotifications')) {
          const { Notification } = require('electron');
          new Notification({
            title: 'Transcription Failed',
            body: 'Failed to process audio: ' + error.message
          }).show();
        }
      });

      return { success: true, audioPath };
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.isRecording = false;
      return { success: false, message: error.message };
    }
  }

  setupIpcHandlers() {
    // App info
    ipcMain.handle('get-app-version', () => {
      return app.getVersion();
    });

    // Window management
    ipcMain.handle('show-settings', () => {
      this.createSettingsWindow();
    });

    // Recording controls
    ipcMain.handle('start-recording', async () => {
      return await this.startRecording();
    });

    ipcMain.handle('stop-recording', async () => {
      return await this.stopRecording();
    });

    ipcMain.handle('get-recording-state', () => {
      return this.isRecording;
    });

    // Audio processing
    ipcMain.handle('process-audio', async (event, audioData) => {
      return await this.processAudio(audioData);
    });

    // Settings
    ipcMain.handle('get-settings', () => {
      return this.settingsService.getSettings();
    });

    ipcMain.handle('save-settings', async (event, settings) => {
      return await this.saveSettings(settings);
    });

    // Text typing
    ipcMain.handle('type-text', async (event, text) => {
      const typingSettings = this.settingsService.getTypingSettings();
      return await this.typingService.typeText(text, typingSettings);
    });

    // Notifications
    ipcMain.handle('show-notification', (event, title, body) => {
      if (this.settingsService.getSetting('showNotifications')) {
        new Notification({ title, body }).show();
      }
    });

    // History management (placeholder for now)
    ipcMain.handle('get-history', () => {
      return []; // TODO: Implement history storage
    });

    ipcMain.handle('save-to-history', (event, transcription) => {
      // TODO: Implement history storage
      return true;
    });

    ipcMain.handle('delete-from-history', (event, id) => {
      // TODO: Implement history storage
      return true;
    });

    ipcMain.handle('export-history', () => {
      // TODO: Implement history export
      return true;
    });
  }

  async processRecordedAudio(audioPath) {
    try {
      console.log('Processing recorded audio:', audioPath);

      // Get transcription settings
      const openAISettings = this.settingsService.getOpenAISettings();

      // Transcribe the audio
      const transcription = await this.transcriptionService.transcribeAudio(audioPath, {
        model: openAISettings.model,
        language: openAISettings.language === 'auto' ? undefined : openAISettings.language
      });

      console.log('Transcription completed:', transcription.text);

      // Enhance with GPT if enabled
      let finalText = transcription.text;
      if (openAISettings.enhanceWithGPT && transcription.text.length > 0) {
        try {
          finalText = await this.transcriptionService.enhanceTranscription(transcription.text);
        } catch (enhanceError) {
          console.warn('Enhancement failed, using original transcription:', enhanceError);
        }
      }

      // Type the text if auto-typing is enabled
      const typingSettings = this.settingsService.getTypingSettings();
      if (typingSettings.autoType && finalText.length > 0) {
        await this.typingService.typeText(finalText, typingSettings);
      }

      // Show notification
      if (this.settingsService.getSetting('showNotifications')) {
        new Notification({
          title: 'Transcription Complete',
          body: finalText.length > 50 ? finalText.substring(0, 50) + '...' : finalText
        }).show();
      }

      // Send transcription to renderer processes
      const transcriptionData = {
        ...transcription,
        text: finalText,
        enhanced: openAISettings.enhanceWithGPT
      };

      if (this.mainWindow) {
        this.mainWindow.webContents.send('transcription-complete', transcriptionData);
      }

      return transcriptionData;
    } catch (error) {
      console.error('Audio processing failed:', error);

      // Show error notification
      if (this.settingsService.getSetting('showNotifications')) {
        new Notification({
          title: 'Transcription Failed',
          body: error.message
        }).show();
      }

      throw error;
    }
  }

  async saveSettings(settings) {
    try {
      // Validate settings
      const validation = this.settingsService.validateSettings(settings);
      if (!validation.valid) {
        throw new Error(`Invalid settings: ${validation.errors.join(', ')}`);
      }

      // Save settings
      this.settingsService.setSettings(settings);

      // Update global shortcut if it changed
      const currentShortcut = this.settingsService.getSetting('shortcut');
      if (settings.shortcut && settings.shortcut !== currentShortcut) {
        this.updateGlobalShortcut(settings.shortcut);
      }

      // Update transcription service API key if it changed
      if (settings.apiKey) {
        this.transcriptionService.setApiKey(settings.apiKey);
      }

      console.log('Settings saved successfully');
      return { success: true };
    } catch (error) {
      console.error('Failed to save settings:', error);
      return { success: false, message: error.message };
    }
  }

  updateGlobalShortcut(newShortcut) {
    try {
      // Unregister current shortcut
      globalShortcut.unregisterAll();

      // Register new shortcut
      globalShortcut.register(newShortcut, () => {
        this.handleVoiceShortcut();
      });

      console.log(`Global shortcut updated to: ${newShortcut}`);
      return true;
    } catch (error) {
      console.error('Failed to update global shortcut:', error);
      // Fallback to default shortcut
      this.registerGlobalShortcuts();
      return false;
    }
  }

  // Icon creation removed for simplicity

  cleanup() {
    // Cleanup services
    if (this.audioService) {
      this.audioService.cleanup();
    }

    // Unregister global shortcuts
    globalShortcut.unregisterAll();

    // Close all windows
    if (this.mainWindow) this.mainWindow.destroy();
    if (this.settingsWindow) this.settingsWindow.destroy();
    if (this.recordingWindow) this.recordingWindow.destroy();

    // Tray removed for simplicity
  }
}

// Create the app instance
new KambaVoiceApp();
