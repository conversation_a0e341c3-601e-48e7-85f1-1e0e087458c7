const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const record = require('node-record-lpcm16');

class AudioService {
  constructor() {
    this.isRecording = false;
    this.audioStream = null;
    this.tempAudioPath = null;
    this.recorder = null;
    this.recordingStartTime = null;
  }

  async startRecording() {
    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      console.log('🎙️ Starting REAL microphone recording...');

      // Create temporary file path
      const tempDir = app.getPath('temp');
      this.tempAudioPath = path.join(tempDir, `kambaavoice_${Date.now()}.wav`);

      // Start real microphone recording
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // Configure recording options
      const recordingOptions = {
        sampleRateHertz: 16000,
        threshold: 0,
        verbose: false,
        recordProgram: 'sox', // or 'rec' on some systems
        silence: '1.0',
        device: null // Use default microphone
      };

      // Start recording to file
      this.recorder = record.record(recordingOptions);

      // Create write stream to save audio
      this.audioStream = fs.createWriteStream(this.tempAudioPath);
      this.recorder.stream().pipe(this.audioStream);

      console.log('🎤 Real microphone recording started, saving to:', this.tempAudioPath);
      console.log('📢 SPEAK NOW - Your voice is being recorded!');

      return this.tempAudioPath;

    } catch (error) {
      console.error('Failed to start real audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  async stopRecording() {
    if (!this.isRecording) {
      throw new Error('Not currently recording');
    }

    try {
      console.log('🛑 Stopping real microphone recording...');

      this.isRecording = false;
      const recordingDuration = Date.now() - this.recordingStartTime;

      // Stop the recorder
      if (this.recorder) {
        this.recorder.stop();
        this.recorder = null;
      }

      // Close the audio stream
      if (this.audioStream) {
        this.audioStream.end();
        this.audioStream = null;
      }

      // Wait a moment for file to be written
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log(`📊 Recording duration: ${recordingDuration}ms`);

      // Check if file exists and has content
      if (fs.existsSync(this.tempAudioPath)) {
        const stats = fs.statSync(this.tempAudioPath);
        if (stats.size > 0) {
          console.log(`🎵 Real audio recording saved: ${this.tempAudioPath} (${stats.size} bytes)`);
          console.log('✅ Ready to send to OpenAI for transcription!');
          const filePath = this.tempAudioPath;
          this.tempAudioPath = null;
          return filePath;
        } else {
          throw new Error('Audio file is empty - no sound was recorded');
        }
      } else {
        throw new Error('Audio file was not created');
      }

    } catch (error) {
      console.error('Failed to stop audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  cleanup() {
    this.isRecording = false;
    this.audioStream = null;
    this.recorder = null;
    
    // Clean up temporary file
    if (this.tempAudioPath && fs.existsSync(this.tempAudioPath)) {
      try {
        fs.unlinkSync(this.tempAudioPath);
        console.log('Temporary audio file cleaned up');
      } catch (error) {
        console.error('Failed to clean up temporary audio file:', error);
      }
    }
    this.tempAudioPath = null;
  }

  getRecordingState() {
    return this.isRecording;
  }

  // Alternative recording method using node-record-lpcm16
  async startRecordingLPCM() {
    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      const record = require('node-record-lpcm16');
      
      // Create temporary file path
      const tempDir = app.getPath('temp');
      this.tempAudioPath = path.join(tempDir, `kambaavoice_${Date.now()}.wav`);
      
      // Configure recording options
      const recordingOptions = {
        sampleRateHertz: 16000,
        threshold: 0.5,
        verbose: false,
        recordProgram: 'rec', // Use 'sox' on Linux/Mac, 'rec' on Windows
        silence: '1.0',
      };
      
      // Start recording
      const recording = record.record(recordingOptions);
      
      // Create write stream
      const outputFileStream = fs.createWriteStream(this.tempAudioPath);
      recording.stream().pipe(outputFileStream);
      
      this.recorder = recording;
      this.isRecording = true;
      
      console.log('LPCM Audio recording started, saving to:', this.tempAudioPath);
      return this.tempAudioPath;
      
    } catch (error) {
      console.error('Failed to start LPCM audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  async stopRecordingLPCM() {
    if (!this.isRecording || !this.recorder) {
      throw new Error('Not currently recording');
    }

    try {
      // Stop the recording
      this.recorder.stop();
      this.isRecording = false;
      
      // Wait a bit for the file to be written completely
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check if file exists and has content
      if (fs.existsSync(this.tempAudioPath)) {
        const stats = fs.statSync(this.tempAudioPath);
        if (stats.size > 0) {
          console.log(`LPCM Audio recording saved: ${this.tempAudioPath} (${stats.size} bytes)`);
          return this.tempAudioPath;
        } else {
          throw new Error('Audio file is empty');
        }
      } else {
        throw new Error('Audio file was not created');
      }
      
    } catch (error) {
      console.error('Failed to stop LPCM audio recording:', error);
      this.cleanup();
      throw error;
    }
  }
}

module.exports = AudioService;
