const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class AudioService {
  constructor() {
    this.isRecording = false;
    this.audioStream = null;
    this.tempAudioPath = null;
    this.recorder = null;
    this.recordingStartTime = null;
  }

  async startRecording() {
    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      console.log('Starting audio recording (placeholder implementation)');

      // Create temporary file path
      const tempDir = app.getPath('temp');
      this.tempAudioPath = path.join(tempDir, `kambaavoice_${Date.now()}.wav`);

      // For now, we'll create a placeholder implementation
      // In a real implementation, you would use Web Audio API or native recording
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // Create a placeholder audio file
      // This would be replaced with actual audio recording
      fs.writeFileSync(this.tempAudioPath, Buffer.alloc(1024, 0));

      console.log('Audio recording started (placeholder), saving to:', this.tempAudioPath);
      return this.tempAudioPath;

      
    } catch (error) {
      console.error('Failed to start audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  async stopRecording() {
    if (!this.isRecording) {
      throw new Error('Not currently recording');
    }

    try {
      console.log('Stopping audio recording (placeholder implementation)');

      this.isRecording = false;
      const recordingDuration = Date.now() - this.recordingStartTime;

      // Simulate recording duration check
      if (recordingDuration < 500) {
        throw new Error('Recording too short');
      }

      // For demo purposes, create a larger placeholder file
      const demoAudioSize = Math.min(recordingDuration * 16, 50000); // Simulate audio data
      fs.writeFileSync(this.tempAudioPath, Buffer.alloc(demoAudioSize, 0));

      // Check if file exists and has content
      if (fs.existsSync(this.tempAudioPath)) {
        const stats = fs.statSync(this.tempAudioPath);
        if (stats.size > 0) {
          console.log(`Audio recording saved: ${this.tempAudioPath} (${stats.size} bytes)`);
          return this.tempAudioPath;
        } else {
          throw new Error('Audio file is empty');
        }
      } else {
        throw new Error('Audio file was not created');
      }

    } catch (error) {
      console.error('Failed to stop audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  cleanup() {
    this.isRecording = false;
    this.audioStream = null;
    this.recorder = null;
    
    // Clean up temporary file
    if (this.tempAudioPath && fs.existsSync(this.tempAudioPath)) {
      try {
        fs.unlinkSync(this.tempAudioPath);
        console.log('Temporary audio file cleaned up');
      } catch (error) {
        console.error('Failed to clean up temporary audio file:', error);
      }
    }
    this.tempAudioPath = null;
  }

  getRecordingState() {
    return this.isRecording;
  }

  // Alternative recording method using node-record-lpcm16
  async startRecordingLPCM() {
    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      const record = require('node-record-lpcm16');
      
      // Create temporary file path
      const tempDir = app.getPath('temp');
      this.tempAudioPath = path.join(tempDir, `kambaavoice_${Date.now()}.wav`);
      
      // Configure recording options
      const recordingOptions = {
        sampleRateHertz: 16000,
        threshold: 0.5,
        verbose: false,
        recordProgram: 'rec', // Use 'sox' on Linux/Mac, 'rec' on Windows
        silence: '1.0',
      };
      
      // Start recording
      const recording = record.record(recordingOptions);
      
      // Create write stream
      const outputFileStream = fs.createWriteStream(this.tempAudioPath);
      recording.stream().pipe(outputFileStream);
      
      this.recorder = recording;
      this.isRecording = true;
      
      console.log('LPCM Audio recording started, saving to:', this.tempAudioPath);
      return this.tempAudioPath;
      
    } catch (error) {
      console.error('Failed to start LPCM audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  async stopRecordingLPCM() {
    if (!this.isRecording || !this.recorder) {
      throw new Error('Not currently recording');
    }

    try {
      // Stop the recording
      this.recorder.stop();
      this.isRecording = false;
      
      // Wait a bit for the file to be written completely
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check if file exists and has content
      if (fs.existsSync(this.tempAudioPath)) {
        const stats = fs.statSync(this.tempAudioPath);
        if (stats.size > 0) {
          console.log(`LPCM Audio recording saved: ${this.tempAudioPath} (${stats.size} bytes)`);
          return this.tempAudioPath;
        } else {
          throw new Error('Audio file is empty');
        }
      } else {
        throw new Error('Audio file was not created');
      }
      
    } catch (error) {
      console.error('Failed to stop LPCM audio recording:', error);
      this.cleanup();
      throw error;
    }
  }
}

module.exports = AudioService;
